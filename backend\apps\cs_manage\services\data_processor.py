"""
数据处理服务

处理七鱼会话数据并计算各项指标
"""
import logging
from typing import List, Dict, Optional
from datetime import datetime

from ..models import DailyMetricGroup
from ..utils.metrics_calculator import MetricsCalculator
from .qiyu_client import QiyuDataClient

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.qiyu_client = QiyuDataClient()
    
    def process_daily_data(self, target_date: datetime) -> bool:
        """
        处理指定日期的数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            处理是否成功
        """
        try:
            logger.info(f"[数据处理] 开始处理 {target_date.date()} 的数据")
            
            # 1. 获取基础数据
            staff_groups = self.qiyu_client.get_staff_groups()
            if not staff_groups:
                logger.error(f"[数据处理] 获取客服组列表失败")
                return False
            
            sessions = self.qiyu_client.get_session_history_by_date(target_date)
            worksheets = self.qiyu_client.get_worksheet_list_by_date(target_date)
            overview = self.qiyu_client.get_statistics_overview_by_date(target_date)  # 全部组别的概览
            # 移除情绪分析数据获取，改为实时计算
            
            # 2. 初始化计算器
            calculator = MetricsCalculator(staff_groups)
            
            # 3. 按组别分组会话数据
            group_sessions_mapping = calculator.get_group_sessions_mapping(sessions)
            
            # 4. 计算并保存各组别指标
            success_count = 0
            total_groups = len(group_sessions_mapping)
            
            for group_id, group_sessions in group_sessions_mapping.items():
                try:
                    # 为每个组别获取专门的headline数据
                    group_overview = None
                    if group_id != 0:  # 非全部组别时获取专门的headline数据
                        group_overview = self.qiyu_client.get_statistics_overview_by_date(target_date, group_id)
                    else:
                        group_overview = overview  # 全部组别使用已获取的数据

                    success = self._process_group_data(
                        target_date=target_date,
                        group_id=group_id,
                        group_sessions=group_sessions,
                        worksheets=worksheets,
                        overview=group_overview,
                        calculator=calculator
                    )
                    
                    if success:
                        success_count += 1
                        
                except Exception as e:
                    logger.error(f"[数据处理] 处理组别 {group_id} 数据失败: {str(e)}")
                    continue
            
            logger.info(f"[数据处理] {target_date.date()} 数据处理完成，成功: {success_count}/{total_groups}")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"[数据处理] 处理 {target_date.date()} 数据失败: {str(e)}")
            return False
    
    def _process_group_data(self,
                           target_date: datetime,
                           group_id: int,
                           group_sessions: List[Dict],
                           worksheets: List[Dict],
                           overview: Optional[Dict],
                           calculator: MetricsCalculator) -> bool:
        """
        处理单个组别的数据
        
        Args:
            target_date: 目标日期
            group_id: 组别ID
            group_sessions: 该组别的会话列表
            worksheets: 工单列表
            overview: 统计概览
            emotion_score: 情绪分值（仅全部组别有效）
            calculator: 指标计算器
            
        Returns:
            处理是否成功
        """
        try:
            # 1. 计算AI会话相关指标（只计算API中没有的）
            ai_metrics = calculator.calculate_ai_session_metrics(group_sessions)

            # 2. 从七鱼API提取指标（直接使用，无需计算）
            qiyu_metrics = calculator.extract_qiyu_metrics(overview if group_id == 0 else None)

            # 3. 计算工单数量
            worksheet_count = calculator.calculate_worksheet_count(worksheets, group_id)

            # 4. 获取组别名称
            group_name = ''
            if group_id != 0:
                group_name = calculator.group_mapping.get(group_id, f'组别{group_id}')

            # 5. 合并数据（只存储计算得出的指标和原始数据）
            metric_data = {
                'date': target_date.date(),
                'group_id': group_id,
                'group_name': group_name,

                # AI相关指标（计算得出，需要存储）
                'ai_sessions': ai_metrics['ai_sessions'],
                'manual_sessions': ai_metrics['manual_sessions'],
                'ai_transfer': ai_metrics['ai_transfer'],

                # 工单数据
                'worksheet_count': worksheet_count,

                # 会话总数（统一计算方式）
                'total_sessions': len(group_sessions),

                # 存储原始数据（仅全部组别，其他指标从此获取）
                'qiyu_raw_data': overview if group_id == 0 else None,
                'data_version': '',
            }
            
            # 使用 update_or_create 避免重复数据
            metric_obj, created = DailyMetricGroup.objects.update_or_create(
                date=target_date.date(),
                group_id=group_id,
                defaults=metric_data
            )
            
            # 计算衍生指标
            metric_obj.calculate_derived_metrics()
            metric_obj.save()
            
            action = "创建" if created else "更新"
            group_display = group_name if group_name else "全部"
            logger.info(f"[数据处理] {action} {target_date.date()} {group_display} 指标成功")
            
            return True
            
        except Exception as e:
            logger.error(f"[数据处理] 处理组别 {group_id} 数据失败: {str(e)}")
            return False
    
    def get_processing_summary(self, target_date: datetime) -> Dict:
        """
        获取处理结果摘要
        
        Args:
            target_date: 目标日期
            
        Returns:
            处理结果摘要
        """
        try:
            metrics = DailyMetricGroup.objects.filter(date=target_date.date())
            
            if not metrics.exists():
                return {
                    'date': target_date.date(),
                    'processed': False,
                    'total_groups': 0,
                    'summary': {}
                }
            
            # 获取全部数据的摘要
            all_data = metrics.filter(group_id=0).first()
            summary = {}
            
            if all_data:
                summary = {
                    'total_sessions': all_data.total_sessions,
                    'ai_sessions': all_data.ai_sessions,
                    'manual_sessions': all_data.manual_sessions,
                    'ai_transfer': all_data.ai_transfer,
                    'worksheet_count': all_data.worksheet_count,
                    'emotion_score': all_data.emotion_score,
                    'manual_satisfaction': all_data.manual_satisfaction,
                    'fcr_ratio': all_data.fcr_ratio,
                    'avg_first_resp': all_data.avg_first_resp,
                    'resp_30_ratio': all_data.resp_30_ratio,
                    'ai_transfer_rate': all_data.ai_transfer_rate,
                    'manual_handle_ai_ratio': all_data.manual_handle_ai_ratio,
                }
            
            return {
                'date': target_date.date(),
                'processed': True,
                'total_groups': metrics.count(),
                'summary': summary
            }
            
        except Exception as e:
            logger.error(f"[数据处理] 获取处理摘要失败: {str(e)}")
            return {
                'date': target_date.date(),
                'processed': False,
                'total_groups': 0,
                'summary': {}
            }
    
    def cleanup_old_data(self, days_to_keep: int = 90) -> int:
        """
        清理旧数据
        
        Args:
            days_to_keep: 保留天数
            
        Returns:
            删除的记录数
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.now().date() - timedelta(days=days_to_keep)
            
            deleted_count, _ = DailyMetricGroup.objects.filter(date__lt=cutoff_date).delete()
            
            logger.info(f"[数据处理] 清理 {cutoff_date} 之前的数据，删除 {deleted_count} 条记录")
            return deleted_count
            
        except Exception as e:
            logger.error(f"[数据处理] 清理旧数据失败: {str(e)}")
            return 0
