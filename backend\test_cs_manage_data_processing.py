#!/usr/bin/env python
"""
CS_MANAGE数据处理功能测试脚本

测试修复后的数据处理功能，验证：
1. 非0组别raw数据存储问题
2. 数据解包功能
3. AI判断逻辑统一
4. 数据获取和显示功能
"""

import os
import sys
import django
from datetime import datetime, timedelta
import json

# 设置Django环境
sys.path.append('/d/project/cs-admin/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings')
django.setup()

from apps.cs_manage.services.data_processor import DataProcessor
from apps.cs_manage.services.qiyu_client import QiyuDataClient
from apps.cs_manage.models import DailyMetricGroup
from apps.cs_manage.utils.metrics_calculator import MetricsCalculator


def test_ai_session_detection():
    """测试AI会话判断逻辑"""
    print("=" * 50)
    print("测试AI会话判断逻辑")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            'name': 'AI转人工会话 - contactCs路径',
            'session': {
                'sessionExt': {
                    'fromPage': 'https://example.com/contactCs/index.html?u=123'
                }
            },
            'expected_ai_related': True,
            'expected_ai_transfer': True
        },
        {
            'name': 'AI会话 - u参数',
            'session': {
                'sessionExt': {
                    'fromPage': 'https://example.com/page?u=456'
                }
            },
            'expected_ai_related': True,
            'expected_ai_transfer': False
        },
        {
            'name': '普通人工会话',
            'session': {
                'sessionExt': {
                    'fromPage': 'https://example.com/normal'
                }
            },
            'expected_ai_related': False,
            'expected_ai_transfer': False
        },
        {
            'name': 'AI会话 - u参数带#',
            'session': {
                'sessionExt': {
                    'fromPage': 'https://example.com/page?u=789#123'
                }
            },
            'expected_ai_related': True,
            'expected_ai_transfer': True
        }
    ]
    
    client = QiyuDataClient()
    
    for case in test_cases:
        print(f"\n测试用例: {case['name']}")
        
        ai_related = client.is_ai_related_session(case['session'])
        ai_transfer = client.is_ai_transfer_session(case['session'])
        
        print(f"  AI相关会话: {ai_related} (期望: {case['expected_ai_related']})")
        print(f"  AI转人工: {ai_transfer} (期望: {case['expected_ai_transfer']})")
        
        # 验证结果
        if ai_related == case['expected_ai_related'] and ai_transfer == case['expected_ai_transfer']:
            print("  ✅ 测试通过")
        else:
            print("  ❌ 测试失败")


def test_data_processing():
    """测试数据处理功能"""
    print("\n" + "=" * 50)
    print("测试数据处理功能")
    print("=" * 50)
    
    # 使用昨天的日期进行测试
    test_date = datetime.now() - timedelta(days=1)
    
    print(f"测试日期: {test_date.date()}")
    
    # 初始化数据处理器
    processor = DataProcessor()
    
    try:
        # 处理数据
        print("\n开始处理数据...")
        success = processor.process_daily_data(test_date)
        
        if success:
            print("✅ 数据处理成功")
            
            # 检查存储的数据
            print("\n检查存储的数据:")
            metrics = DailyMetricGroup.objects.filter(date=test_date.date())
            
            for metric in metrics:
                print(f"\n组别 {metric.group_id} ({metric.group_name or '全部'}):")
                print(f"  总会话数: {metric.total_sessions}")
                print(f"  AI会话数: {metric.ai_sessions}")
                print(f"  人工会话数: {metric.manual_sessions}")
                print(f"  AI转人工数: {metric.ai_transfer}")
                print(f"  工单数: {metric.worksheet_count}")
                print(f"  数据版本: {metric.data_version}")
                
                # 检查raw数据
                if metric.qiyu_raw_data:
                    print(f"  ✅ 有raw数据 (字段数: {len(metric.qiyu_raw_data)})")
                    
                    # 检查从raw数据获取的属性
                    print(f"  在线接入率: {metric.online_ratio}%")
                    print(f"  人工满意度: {metric.manual_satisfaction}%")
                    print(f"  FCR率: {metric.fcr_ratio}%")
                    print(f"  平均首响: {metric.avg_first_resp}秒")
                    print(f"  30秒应答率: {metric.resp_30_ratio}%")
                else:
                    print(f"  ❌ 无raw数据")
        else:
            print("❌ 数据处理失败")
            
    except Exception as e:
        print(f"❌ 数据处理异常: {str(e)}")


def test_data_retrieval():
    """测试数据获取功能"""
    print("\n" + "=" * 50)
    print("测试数据获取功能")
    print("=" * 50)
    
    test_date = datetime.now() - timedelta(days=1)
    
    try:
        # 获取处理摘要
        processor = DataProcessor()
        summary = processor.get_processing_summary(test_date)
        
        print(f"处理摘要:")
        print(f"  日期: {summary['date']}")
        print(f"  已处理: {summary['processed']}")
        print(f"  组别数: {summary['total_groups']}")
        
        if summary['summary']:
            print(f"  汇总数据:")
            for key, value in summary['summary'].items():
                print(f"    {key}: {value}")
        
    except Exception as e:
        print(f"❌ 数据获取异常: {str(e)}")


def main():
    """主函数"""
    print("CS_MANAGE数据处理功能测试")
    print("测试修复后的功能，包括非0组别raw数据存储、数据解包、AI判断逻辑等")
    
    # 测试AI会话判断逻辑
    test_ai_session_detection()
    
    # 测试数据处理功能
    test_data_processing()
    
    # 测试数据获取功能
    test_data_retrieval()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)


if __name__ == "__main__":
    main()
